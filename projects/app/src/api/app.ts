import { POST } from '@/utils/request';
import { PagingData } from '@/types';

import {
  CreateAppParams,
  deleteAppParams,
  AppUpdateParams,
  AppListItemType,
  GetPersonalAppPageParams,
  GetTenantAppPageParams,
  GetMyAppPageParams,
  GetAppListParams,
  GetPersonalAppListParams,
  GetTenantAppListParams,
  AppDefaultAppsType,
  AppUpdateConfigParams,
  AppUpdateTenantAppConfigParams
} from '@/types/api/app';
import { DataSource } from '@/constants/common';
import { getListFromPage } from '@/utils/api';
/**
 * 获取模型分页列表
 */
export const getMyAppPage = (data: GetMyAppPageParams) =>
  POST<PagingData<AppListItemType>>('/admin/app/page', data);

/**
/**
 * 获取租户模型分页列表
 */
export const getAppList = (data: GetAppListParams) =>
  POST<AppListItemType[]>('/admin/app/list', data);

/**
 * 获取个人模型分页列表
 */
export const getPersonalAppPage = (data: GetPersonalAppPageParams) =>
  POST<PagingData<AppListItemType>>('/admin/app/personal/page', data);

/**
 * 获取租户模型分页列表
 */
export const getTenantAppPage = (data: GetTenantAppPageParams) =>
  POST<PagingData<AppListItemType>>('/admin/app/tenant/page', data);

/**
 * 获取个人模型分页列表
 */
export const getPersonalAppList = (data: GetPersonalAppListParams) =>
  POST<AppListItemType[]>('/admin/app/personal/list', data);

/**
 * 获取租户模型分页列表
 */
export const getTenantAppList = (data: GetTenantAppListParams) =>
  POST<AppListItemType[]>('/admin/app/tenant/list', data);

/**
 * 创建一个模型
 */
export const createApp = (data: CreateAppParams) => POST<string>('/admin/app/create', data);

/**
 * 根据 ID 删除模型
 */
export const deleteAppModel = (data: deleteAppParams) => POST('/admin/app/delete', data);

/**
 * 根据 ID 更新模型
 */
export const updateApp = (data: AppUpdateParams) => POST('/admin/app/update', data);

/**
 * 取消置顶
 */
export const cancelTop = (data: { id: string }) => POST('/admin/app/cancelTop', data);

/**
 * 从其他类型导入\
 */
export const importFromOtherType = (data: {
  createUsername?: string;
  industry: string;
  otherIndustry: string;
  updateUsername?: string;
}) => POST('/admin/app/importFromOtherType', data);

/**
 * 移入应用
 */
export const moveApp = (data: { appIds: string[]; labelId: string; sceneId: string }) =>
  POST('/admin/app/move', data);

/**
 * 移出应用
 */
export const removeApp = (data: { appId: string; labelId: string; sceneId: string }) =>
  POST('/admin/app/remove', data);

/**
 * 置顶
 */
export const topApp = (data: { id: string }) => POST('/admin/app/top', data);

export const cancelTopApp = (data: { id: string }) => POST('/admin/app/cancelTop', data);

export const updateTenantApp = (data: AppUpdateParams) => POST('/admin/app/updateTenantApp', data);

export const updateTenantAppStatus = (data: { id: string; status: number }) =>
  POST('/admin/app/updateTenantAppStatus', data);

export const deleteTenantApp = (data: { id: string; tmbId: string }) =>
  POST('/admin/app/deleteTenantApp', data);

/**
 * 更新应用状态
 */
export const updateAppStatus = (data: { id: string; status: number }) =>
  POST('/admin/app/updateStatus', data);

//判断应用是否关联工作流（类型公共应用）
export const getValidAppWorkflow = (data: { id: string }) =>
  POST('/admin/app/validAppWorkflow', data);

//判断应用是否关联工作流（租户公共应用&个人应用）
export const getValidTenantAppWorkflow = (data: { id: string }) =>
  POST('/admin/app/validTenantAppWorkflow', data);

// 设置系统默认应用
export const updateDefaultApp = (data: { id: string }) => POST('/admin/app/updateDefault', data);

// 获取系统默认应用
export const getAppDefaultApps = (data: { industry: string }) =>
  POST<AppDefaultAppsType>('/admin/app/defaultApps', data);

// 修改公开/私有配置
export const setAppUpdateConfig = (data: AppUpdateConfigParams) =>
  POST('/admin/app/updateConfig', data);

export const setAppUpdateEditStatus = (data: { id: string }) =>
  POST('/admin/app/updateEditStatus', data);

// 修改租户应用公开/私有配置
export const setAppUpdateTenantAppConfig = (data: AppUpdateTenantAppConfigParams) =>
  POST('/admin/app/updateTenantAppConfig', data);

/**
 * 获取应用列表
 */
export const getSelectAppList = async (
  data: GetAppListParams & GetTenantAppListParams & GetPersonalAppListParams,
  source: DataSource
) => {
  const { industry, tenantId, tmbId } = data;

  if (source == DataSource.Offical || source == undefined) {
    return getListFromPage(getMyAppPage, { industry }, 9999).then((res) => {
      return res.map((item) => {
        return {
          ...item,
          source: DataSource.Offical
        };
      });
    });
  } else if (source == DataSource.Tenant) {
    return getTenantAppList({ tenantId }) as Promise<AppListItemType[]>;
  } else {
    return getPersonalAppList({ tenantId, tmbId }) as Promise<AppListItemType[]>;
  }
};
// 0-不展示 1-展示
export const isShowInApp = (data: { id: string; isShowApp: boolean }) =>
  POST('/admin/app/isShowInApp', data);
